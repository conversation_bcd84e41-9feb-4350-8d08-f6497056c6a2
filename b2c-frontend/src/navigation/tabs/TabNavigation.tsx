import type React from 'react';
import { useRef, useEffect } from 'react';
import { Pressable, View, Animated, Text } from 'react-native';
import {
  createBottomTabNavigator,
  type BottomTabBarProps,
  type BottomTabBarButtonProps,
} from '@react-navigation/bottom-tabs';
import { getFocusedRouteNameFromRoute } from '@react-navigation/native';
import CreateStackNavigator from '@/src/navigation/stacks/CreateStack';
import HomeStackNavigator from '@/src/navigation/stacks/HomeStack';
import NotificationStackNavigator from '@/src/navigation/stacks/NotificationStack';
import AnalysisTab from '@/src/assets/svgs/AnalysisTab';
import HomeIcon from '@/src/assets/svgs/HomeTab';
import LearnAndCollabIcon from '@/src/assets/svgs/LearnCollabTab';
import Location from '@/src/assets/svgs/Location';
import NotificationIcon from '@/src/assets/svgs/NotificationTab';
import LearnCollabStackNavigator from '../stacks/LearnCollabStack';
import NearbyStackNavigator from '../stacks/NearbyStack';
import type { HomeScreenActionsRef } from '../types';

const Tab = createBottomTabNavigator();

const TabButton = (props: BottomTabBarButtonProps) => {
  const scale = useRef(new Animated.Value(1)).current;
  const handlePressIn = () => {
    Animated.spring(scale, { toValue: 0.95, useNativeDriver: true }).start();
  };
  const handlePressOut = () => {
    Animated.spring(scale, { toValue: 1, useNativeDriver: true }).start();
  };

  return (
    <Pressable
      onPress={props.onPress}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      style={props.style}
      accessibilityRole={props.accessibilityRole}
    >
      <Animated.View style={{ transform: [{ scale }] }}>{props.children}</Animated.View>
    </Pressable>
  );
};

const CustomTabBar = ({ state, descriptors, navigation }: BottomTabBarProps) => {
  const translateY = useRef(new Animated.Value(0)).current;
  const currentRoute = state.routes[state.index];
  const currentOptions = descriptors[currentRoute.key]?.options;
  const shouldHide =
    currentOptions?.tabBarStyle &&
    typeof currentOptions.tabBarStyle === 'object' &&
    'display' in currentOptions.tabBarStyle &&
    currentOptions.tabBarStyle.display === 'none';

  useEffect(() => {
    Animated.timing(translateY, {
      toValue: shouldHide ? 100 : 0,
      duration: 200,
      useNativeDriver: true,
    }).start();
  }, [shouldHide, translateY]);

  return (
    <Animated.View
      style={[
        {
          position: 'absolute',
          bottom: 0,
          left: 0,
          right: 0,
          backgroundColor: '#ffffff',
          height: 80,
          flexDirection: 'row',
          paddingHorizontal: 20,
          paddingBottom: 25,
          paddingTop: 15,
          borderTopWidth: 1,
          borderTopColor: '#f0f0f0',
          borderRadius: 20,
        },
        { transform: [{ translateY }] },
      ]}
    >
      {state.routes.map((route, index) => {
        const { options } = descriptors[route.key];
        const isFocused = state.index === index;
        const onPress = () => {
          const event = navigation.emit({
            type: 'tabPress',
            target: route.key,
            canPreventDefault: true,
          });

          if (!isFocused && !event.defaultPrevented) {
            navigation.navigate(route.name, route.params);
          }
        };

        return (
          <View key={route.key} style={{ flex: 1, alignItems: 'center', justifyContent: 'center' }}>
            <TabButton
              onPress={onPress}
              style={{ alignItems: 'center', justifyContent: 'center', flex: 1, width: '100%' }}
              accessibilityRole="button"
            >
              {options.tabBarIcon?.({ focused: isFocused, color: '', size: 0 })}
            </TabButton>
          </View>
        );
      })}
    </Animated.View>
  );
};

const TabIcon = ({
  IconComponent,
  focused,
  routeName,
}: {
  IconComponent: React.ComponentType<{ fill: string }>;
  focused: boolean;
  routeName: string;
}) => {
  const isHomeTab = routeName === 'HomeStack';
  const backgroundColor = focused ? '#DDEFC8' : 'transparent';
  const fillColor = isHomeTab ? (focused ? '#006400' : '#000000') : focused ? '#448600' : '#000000';

  return (
    <View
      style={{
        alignItems: 'center',
        justifyContent: 'center',
        paddingHorizontal: 16,
        paddingVertical: 8,
        borderRadius: 20,
        overflow: 'hidden',
        backgroundColor,
      }}
    >
      <IconComponent fill={fillColor} />
    </View>
  );
};

const CreateButton = ({ focused }: { focused: boolean }) => (
  <View style={{ alignItems: 'center', justifyContent: 'center' }}>
    <AnalysisTab fill="#000000" width={2.5} height={2.5} />
  </View>
);

const hiddenScreens = {
  home: [
    'Chat',
    'Chats',
    'AIChat',
    'GlobalSearch',
    'Comment',
    'OtherUserProfile',
    'PortProfile',
    'ShipProfile',
    'EditDocumentList',
    'EditCertificationList',
    'EditEducationList',
    'EditSkillsList',
    'EditExperienceList',
    'Connection',
    'Likes',
    'EditShipItem',
    'PortsVisited',
    'UserSettings',
    'ReferralDetails',
    'Leaderboard',
  ],
  create: ['CreateContent'],
  learnCollab: ['ForumAnswers', 'CreateQuestion', 'ForumComments', 'SearchScreen'],
  notification: ['ForumAnswers', 'ForumComments', 'Like', 'Comments', 'OtherUserProfile'],
  nearby: [],
};

const getTabOptions = (route: any, screens: string[]) => {
  const routeName = getFocusedRouteNameFromRoute(route) ?? 'Home';
  return screens.includes(routeName)
    ? { tabBarStyle: { display: 'none' as const } }
    : { borderRadius: 20 };
};

const BottomTabNavigator = () => {
  const homeScreenActionsRef = useRef<HomeScreenActionsRef['current']>({});
  return (
    <Tab.Navigator
      screenOptions={{
        headerShown: false,
        tabBarShowLabel: false,
        tabBarButton: TabButton,
      }}
      tabBar={(props) => <CustomTabBar {...props} />}
    >
      <Tab.Screen
        name="HomeStack"
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <TabIcon IconComponent={HomeIcon} focused={focused} routeName="HomeStack" />
          ),
          ...getTabOptions(route, hiddenScreens.home),
        })}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            const state = navigation.getState();
            const currentRoute = state.routes[state.index];
            const isAlreadyHome =
              currentRoute.name === 'HomeStack' &&
              getFocusedRouteNameFromRoute(currentRoute) === 'Home';
            if (isAlreadyHome) {
              homeScreenActionsRef.current.scrollToTop?.();
              homeScreenActionsRef.current.handleRefresh?.();
            } else {
              e.preventDefault();
              navigation.navigate('HomeStack', { screen: 'Home', params: {} });
            }
          },
        })}
      >
        {() => <HomeStackNavigator homeScreenActionsRef={homeScreenActionsRef} />}
      </Tab.Screen>
      <Tab.Screen
        name="LearnCollabStack"
        component={LearnCollabStackNavigator}
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <TabIcon
              IconComponent={LearnAndCollabIcon}
              focused={focused}
              routeName="LearnCollabStack"
            />
          ),
          ...getTabOptions(route, hiddenScreens.learnCollab),
        })}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            e.preventDefault();
            navigation.navigate('LearnCollabStack', { screen: 'LearnCollab', params: {} });
          },
        })}
      />
      <Tab.Screen
        name="CreateStack"
        component={CreateStackNavigator}
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => <CreateButton focused={focused} />,
          ...getTabOptions(route, hiddenScreens.create),
        })}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            e.preventDefault();
            navigation.navigate('CreateStack', {
              screen: 'CreateContent',
              params: {
                type: 'USER_POST',
                editing: undefined,
                portUnLocode: undefined,
                postId: undefined,
              },
            });
          },
        })}
      />
      <Tab.Screen
        name="NotificationStack"
        component={NotificationStackNavigator}
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <TabIcon
              IconComponent={NotificationIcon}
              focused={focused}
              routeName="NotificationStack"
            />
          ),
          ...getTabOptions(route, hiddenScreens.notification),
        })}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            e.preventDefault();
            navigation.navigate('NotificationStack', { screen: 'Notification', params: {} });
          },
        })}
      />
      <Tab.Screen
        name="NearbyStack"
        component={NearbyStackNavigator}
        options={({ route }) => ({
          tabBarIcon: ({ focused }: { focused: boolean }) => (
            <TabIcon IconComponent={Location} focused={focused} routeName="NearbyStack" />
          ),
          ...getTabOptions(route, hiddenScreens.nearby),
        })}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            e.preventDefault();
            navigation.navigate('NearbyStack', { screen: 'Nearby', params: {} });
          },
        })}
      />
    </Tab.Navigator>
  );
};

export default BottomTabNavigator;
