import { Text, View, ScrollView, Pressable } from 'react-native';
import { createDrawerNavigator, DrawerItemList } from '@react-navigation/drawer';
import LinearGradient from 'react-native-linear-gradient';
import { useSelector } from 'react-redux';
import SafeArea from '@/src/components/SafeArea';
import UserAvatar from '@/src/components/UserAvatar';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import ProfileStackNavigator from '@/src/navigation/stacks/ProfileStack';
import BottomTabNavigator from '@/src/navigation/tabs/TabNavigation';
import Settings from '@/src/assets/svgs/Settings';

const Drawer = createDrawerNavigator();

const CustomDrawerContent = (props: any) => {
  const currentUser = useSelector(selectCurrentUser);
  const userDescription = currentUser?.description || 'No Bio';
  const userDesignation = currentUser?.designation?.name || 'Not specified';
  const userOrganisation = currentUser?.organisation?.name || 'Not specified';
  const userLocation = currentUser?.country?.name || 'Not specified';

  const handleSettingsPress = () => {
    props.navigation.navigate('MainTabs', {
      screen: 'HomeStack',
      params: { screen: 'UserSettings' },
    });
  };

  const handleInviteFriendPress = () => {
    props.navigation.navigate('MainTabs', {
      screen: 'HomeStack',
      params: { screen: 'ReferralDetails' },
    });
  };

  return (
    <SafeArea>
      <View className="flex-1 bg-white shadow-xl">
        <ScrollView className="flex-1" showsVerticalScrollIndicator={false}>
          <View className="px-6 pt-10 items-start border-b border-gray-100">
            <Pressable onPress={() => props.navigation.navigate('ProfileStack')}>
              <UserAvatar
                avatarUri={currentUser?.avatar}
                name={currentUser?.fullName}
                width={80}
                height={80}
                className="border-4 border-green-800/20 rounded-full -ml-3"
              />
            </Pressable>
            <Text className="text-2xl font-extrabold mt-4 text-gray-900">
              {currentUser?.fullName || 'Ritvik'}
            </Text>
            <Text
              className="text-sm text-gray-600 my-2 leading-relaxed italic opacity-90"
              numberOfLines={3}
              ellipsizeMode="tail"
            >
              {userDescription}
            </Text>
            <Text className="text-sm text-gray-500 mt-1">{userDesignation}</Text>
            <Text className="text-sm text-gray-500 mt-1">{userOrganisation}</Text>
            <Text className="text-sm text-gray-500 mt-1 mb-6">{userLocation}</Text>
          </View>
          <LinearGradient
            colors={['#FFD700', '#FFC107']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Pressable
              className="p-5 flex-row items-center gap-4 my-4 rounded-2xl shadow-xl overflow-hidden"
              onPress={handleInviteFriendPress}
            >
              <View className="ml-2 flex-1">
                <Text className="text-xl font-extrabold text-gray-900 tracking-tight">
                  INVITE A FRIEND
                </Text>
                <Text className="text-base text-gray-800 opacity-90 mt-1">
                  Share Navicater, earn rewards!
                </Text>
                <Text className="text-sm text-gray-700 opacity-80 mt-1">
                  10 points per referral.
                </Text>
              </View>
            </Pressable>
          </LinearGradient>
          {/* <LinearGradient
            colors={['#facc15', '#eab308']}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 0 }}
          >
            <Pressable
              className="p-5 flex-row items-center gap-4 my-4 rounded-2xl shadow-xl overflow-hidden"
              // onPress={handleStayConnectedPress}
            >
              <View className="ml-2 flex-1">
                <Text className="text-xl font-extrabold text-gray-900 tracking-tight">
                  STAY CONNECTED
                </Text>
                <Text className="text-base text-gray-800 opacity-90 mt-1">
                  Connecting maritime professionals worldwide
                </Text>
                <Text className="text-sm text-gray-700 opacity-80 mt-1">
                  Exciting updates coming soon.
                </Text>
              </View>
            </Pressable>
          </LinearGradient> */}
          <View className="flex-1 mt-2">
            <DrawerItemList {...props} />
          </View>
        </ScrollView>
        <View className="border-t border-gray-200 bg-white">
          <Pressable
            onPress={handleSettingsPress}
            className="flex-row items-center px-6 pb-12 pt-6 active:bg-gray-100"
          >
            <View className="mr-4 scale-110">
              <Settings width={3} height={3} fill="#4B5563" />
            </View>
            <Text className="text-base font-semibold text-gray-700 tracking-wide">Settings</Text>
          </Pressable>
        </View>
      </View>
    </SafeArea>
  );
};

const DrawerNavigator = () => {
  return (
    <Drawer.Navigator
      screenOptions={{
        headerShown: false,
        drawerStyle: {
          width: '80%',
          backgroundColor: '#ffffff',
        },
        drawerLabelStyle: {
          fontSize: 17,
          color: '#1F2937',
          fontWeight: '600',
          marginLeft: -16,
        },
        drawerActiveBackgroundColor: 'transparent',
        drawerInactiveBackgroundColor: 'transparent',
        drawerItemStyle: {
          marginHorizontal: 16,
          marginVertical: 5,
        },
      }}
      drawerContent={(props) => <CustomDrawerContent {...props} />}
    >
      <Drawer.Screen
        name="MainTabs"
        component={BottomTabNavigator}
        options={{
          title: 'Home',
        }}
      />
      <Drawer.Screen
        name="ProfileStack"
        component={ProfileStackNavigator}
        options={{
          title: 'Profile',
        }}
        initialParams={{
          screen: 'UserProfile',
          params: { profileId: undefined, fromTabPress: true },
        }}
      />
    </Drawer.Navigator>
  );
};

export default DrawerNavigator;
