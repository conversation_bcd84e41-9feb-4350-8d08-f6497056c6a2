import type { NearbyStackParamListI, StackScreenI } from '@/src/navigation/types';
import EditAnnouncementScreen from '@/src/screens/EditAnnouncement';
import EntitySearchScreen from '@/src/screens/EntitySearch';
import LocationScreen from '@/src/screens/Location';
import { NearbyScreen } from '@/src/screens/Nearby';
import { NearbySettingsScreen } from '@/src/screens/NearbySettings';
import PeopleAttendingScreen from '@/src/screens/PeopleAttending';
import UserProfileScreen from '@/src/screens/UserProfile';
import { withErrorBoundary } from '@/src/hocs/withErrorBoundary';

const createErrorBoundaryScreen = (Component: any, title: string, subtitle: string) =>
  withErrorBoundary(Component, { title, subtitle });

const screenConfigs = [
  {
    name: 'Nearby',
    component: NearbyScreen,
    title: 'Nearby Error',
    subtitle: 'Something went wrong loading nearby. Please try again.',
  },
  {
    name: 'EditAnnouncement',
    component: EditAnnouncementScreen,
    title: 'Nearby Error',
    subtitle: 'Something went wrong loading nearby. Please try again.',
  },
  {
    name: 'Location',
    component: LocationScreen,
    title: 'Location Error',
    subtitle: 'Something went wrong loading location. Please try again.',
  },
  {
    name: 'NearbySettings',
    component: NearbySettingsScreen,
    title: 'Settings Error',
    subtitle: 'Something went wrong during settings. Please try again.',
  },
  {
    name: 'PeopleAttending',
    component: PeopleAttendingScreen,
    title: 'People Error',
    subtitle: 'Something went wrong people. Please try again.',
  },
  {
    name: 'SearchScreen',
    component: EntitySearchScreen,
    title: 'Search Error',
    subtitle: 'Something went wrong during search. Please try again.',
  },
  {
    name: 'OtherUserProfile',
    component: UserProfileScreen,
    title: 'Profile Error',
    subtitle: 'Something went wrong loading the profile. Please try again.',
  },
];

export const screens: StackScreenI<NearbyStackParamListI>[] = screenConfigs.map(
  ({ name, component, title, subtitle }) => ({
    name: name as keyof NearbyStackParamListI,
    component: createErrorBoundaryScreen(component, title, subtitle),
  }),
);
