import { Image, Pressable, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { useSelector, useDispatch } from 'react-redux';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import { formatSocialTime } from '@/src/utilities/datetime';
import { BottomTabNavigationI } from '@/src/navigation/types';
import AddConnection from '@/src/assets/svgs/AddConnection';
import Comment from '@/src/assets/svgs/Comment';
import Like from '@/src/assets/svgs/Like';
import { fetchPostAPI } from '@/src/networks/content/post';
import type { NotificationType } from '@/src/networks/notifications/types';
import { fetchForumQuestionDetail } from '@/src/redux/slices/forum/forumSlice';
import type { AppDispatch } from '@/src/redux/store';
import UserAvatar from '../UserAvatar';
import type { NotificationItemProps } from './types';

const NotificationItem = ({ notification, profile, post, onMarkAsRead }: NotificationItemProps) => {
  const navigation = useNavigation<BottomTabNavigationI>();
  const dispatch = useDispatch<AppDispatch>();
  const currentUser = useSelector(selectCurrentUser);
  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'LIKE':
        return <Like isLiked width={1.8} height={1.8} color="#EF4444" />;
      case 'COMMENT':
      case 'REPLY':
      case 'FORUM_QUESTION_COMMENT':
      case 'FORUM_QUESTION_REPLY':
      case 'FORUM_ANSWER_COMMENT':
      case 'FORUM_ANSWER_REPLY':
        return <Comment width={1.8} height={1.8} color="#4CAF50" />;
      case 'FOLLOWER':
      case 'REQUEST_RECEIVED':
      case 'REQUEST_ACCEPTED':
        return <AddConnection width={1.8} height={1.8} color="#4CAF50" />;
      case 'MESSAGE':
        return <Text className="text-xs">💌</Text>;
      case 'FORUM_ANSWER':
        return <Text className="text-xs">📝</Text>;
      case 'FORUM_QUESTION_LIVE':
      case 'FORUM_QUESTIONS':
        return <Text className="text-xs">🚢</Text>;
      case 'FORUM_QUESTION_VOTE':
      case 'FORUM_ANSWER_VOTE':
        return <Text className="text-xs">🗳️</Text>;
      case 'FORUM_ANSWER_VERIFIED':
        return <Text className="text-xs">✅</Text>;
      default:
        return <Text className="text-xs">🔔</Text>;
    }
  };

  const notificationConfig: Record<
    NotificationType | string,
    { message: string; bgColor: string }
  > = {
    LIKE: { message: 'liked your post', bgColor: '#FEF2F2' },
    COMMENT: { message: 'commented on your post', bgColor: '#F0FDF4' },
    REPLY: { message: 'replied to your comment', bgColor: '#F0FDF4' },
    FOLLOWER: { message: 'started following you', bgColor: '#F0FDF4' },
    REQUEST_RECEIVED: { message: 'sent you a connection request', bgColor: '#FFFBEB' },
    REQUEST_ACCEPTED: { message: 'accepted your connection request', bgColor: '#F0FDF4' },
    MESSAGE: { message: 'sent you a message', bgColor: '#FDF4FF' },
    FORUM_ANSWER: { message: 'answered your question', bgColor: '#EFF6FF' },
    FORUM_QUESTION_LIVE: { message: 'asked a new question', bgColor: '#F0F9FF' },
    FORUM_QUESTION_VOTE: { message: 'voted on your question', bgColor: '#F3E8FF' },
    FORUM_QUESTIONS: { message: 'questions are available to answer', bgColor: '#F0F9FF' },
    FORUM_ANSWER_VOTE: { message: 'voted on your answer', bgColor: '#F3E8FF' },
    FORUM_QUESTION_COMMENT: { message: 'commented on your question', bgColor: '#F0FDF4' },
    FORUM_QUESTION_REPLY: { message: 'replied to your comment', bgColor: '#F0FDF4' },
    FORUM_ANSWER_COMMENT: { message: 'commented on your answer', bgColor: '#F0FDF4' },
    FORUM_ANSWER_REPLY: { message: 'replied to your comment', bgColor: '#F0FDF4' },
    FORUM_ANSWER_VERIFIED: { message: 'verified your answer', bgColor: '#ECFDF5' },
  };

  const config = notificationConfig[notification.type] || {
    message: '',
    bgColor: '#F9FAFB',
  };

  const getNotificationMessage = () => {
    if (notification.type === 'FORUM_QUESTIONS') {
      return notification.content.body;
    }

    if (post?.caption && post.caption.length > 50) {
      return `${config.message}: "${post.caption.substring(0, 50)}..."`;
    }
    return post?.caption ? `${config.message}: "${post.caption}..."` : config.message;
  };

  const handlePress = async () => {
    if (!notification.isRead && onMarkAsRead) {
      try {
        await onMarkAsRead(notification.id);
      } catch {}
    }

    const { data } = notification;
    const { actorProfileId, postId, questionId, answerId, commentId, parentCommentId } = data;

    try {
      if (
        notification.type === 'FORUM_ANSWER' ||
        notification.type === 'FORUM_ANSWER_VOTE' ||
        notification.type === 'FORUM_ANSWER_VERIFIED'
      ) {
        if (questionId) {
          await dispatch(fetchForumQuestionDetail({ questionId })).unwrap();
          navigation.navigate('NotificationStack', {
            screen: 'ForumAnswers',
            params: { postId: questionId },
          });
        }
      } else if (
        notification.type === 'FORUM_QUESTION_LIVE' ||
        notification.type === 'FORUM_QUESTION_VOTE'
      ) {
        if (questionId) {
          await dispatch(fetchForumQuestionDetail({ questionId })).unwrap();
          navigation.navigate('NotificationStack', {
            screen: 'ForumAnswers',
            params: { postId: questionId },
          });
        }
      } else if (
        notification.type === 'FORUM_QUESTION_COMMENT' ||
        notification.type === 'FORUM_QUESTION_REPLY'
      ) {
        if (questionId) {
          await dispatch(fetchForumQuestionDetail({ questionId })).unwrap();
          navigation.navigate('NotificationStack', {
            screen: 'ForumAnswers',
            params: { postId: questionId },
          });
        }
      } else if (
        notification.type === 'FORUM_ANSWER_COMMENT' ||
        notification.type === 'FORUM_ANSWER_REPLY'
      ) {
        if (questionId) {
          await dispatch(fetchForumQuestionDetail({ questionId })).unwrap();
          navigation.navigate('NotificationStack', {
            screen: 'ForumAnswers',
            params: { postId: questionId },
          });
        }
      } else if (notification.type === 'FORUM_QUESTIONS') {
        navigation.navigate('NotificationStack', {
          screen: 'Forum',
          params: undefined,
        });
      } else if ((commentId || parentCommentId) && postId) {
        await fetchPostAPI(postId);
        navigation.navigate('NotificationStack', {
          screen: 'Comment',
          params: { postId, type: 'USER_POST' },
        });
      } else if (postId) {
        await fetchPostAPI(postId);
        navigation.navigate('NotificationStack', {
          screen: 'Comment',
          params: { postId, type: 'USER_POST' },
        });
      } else if (actorProfileId && currentUser.profileId !== actorProfileId) {
        navigation.navigate('NotificationStack', {
          screen: 'OtherUserProfile',
          params: {
            profileId: actorProfileId,
            fromTabPress: false,
          },
        });
      }
    } catch {}
  };

  return (
    <Pressable
      className={`mx-3 mb-2 rounded-xl overflow-hidden ${
        notification.isRead ? 'bg-white' : 'bg-green-50 border border-green-100'
      }`}
      onPress={handlePress}
    >
      <View className="p-4">
        <View className="flex-row items-start">
          <View className="relative mr-3">
            <UserAvatar
              avatarUri={profile?.avatar ?? null}
              name={profile?.name}
              width={48}
              height={48}
            />

            <View
              className="absolute -bottom-1 -right-1 w-6 h-6 rounded-full items-center justify-center border-2 border-white"
              style={{ backgroundColor: config.bgColor }}
            >
              {getNotificationIcon(notification.type)}
            </View>

            {!notification.isRead && (
              <View className="absolute -top-1 -left-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white" />
            )}
          </View>

          <View className="flex-1 min-w-0">
            <View className="flex-row items-start justify-between mb-1">
              <Text
                className={`font-semibold text-base ${notification.isRead ? 'text-gray-900' : 'text-gray-900'}`}
                numberOfLines={1}
              >
                {notification.type === 'PUBLIC' || notification.type === 'FORUM_QUESTIONS'
                  ? 'System'
                  : profile?.name || 'Unknown User'}
              </Text>
              <Text className="text-gray-400 text-xs ml-2 mt-0.5">
                {formatSocialTime(notification.createdAt)}
              </Text>
            </View>

            <Text
              className={`text-sm leading-5 ${notification.isRead ? 'text-gray-600' : 'text-gray-700'}`}
              numberOfLines={3}
            >
              {getNotificationMessage()}
            </Text>

            {post?.image && (
              <View className="mt-3 rounded-lg overflow-hidden bg-gray-100">
                <Image source={{ uri: post.image }} className="w-full h-32" resizeMode="cover" />
              </View>
            )}
          </View>
        </View>
      </View>
    </Pressable>
  );
};

export default NotificationItem;
