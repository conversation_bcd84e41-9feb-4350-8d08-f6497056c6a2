import { useState, useRef } from 'react';
import { Pressable, Text, View } from 'react-native';
import PrivacyModal from '@/src/components/PrivacyModal';
import TermsModal from '@/src/components/TermsModal';
import Information from '@/src/assets/svgs/Information';
import BottomSheet from '../Bottomsheet';
import type { InfoModalProps } from './types';

const InfoTexts = {
  country:
    'Your work profile and country help us personalize your experience — connecting you with relevant peers, discussions, and content across the platform.',
  workDetails:
    'Your work profile and country help us personalize your experience — connecting you with relevant peers, discussions, and content across the platform.',
};

const InfoModal = ({ type }: InfoModalProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showPrivacyModal, setShowPrivacyModal] = useState(false);
  const [showTermsModal, setShowTermsModal] = useState(false);

  const deferredActionRef = useRef<(() => void) | null>(null);

  const handlePrivacyPress = () => {
    deferredActionRef.current = () => setShowPrivacyModal(true);
    setIsVisible(false);
  };

  const handleTermsPress = () => {
    deferredActionRef.current = () => setShowTermsModal(true);
    setIsVisible(false);
  };

  const handleBottomSheetHide = () => {
    if (deferredActionRef.current) {
      setTimeout(() => {
        deferredActionRef.current?.();
        deferredActionRef.current = null;
      }, 100);
    }
  };

  return (
    <>
      <Pressable className="flex-row items-center gap-1" onPress={() => setIsVisible(!isVisible)}>
        <Information width={2} height={2} />
        <Text className="text-small text-subLabelGray ml-1">Why is this information useful?</Text>
      </Pressable>

      <BottomSheet
        visible={isVisible}
        onClose={() => setIsVisible(false)}
        height={330}
        onModalHide={handleBottomSheetHide}
      >
        <View className="px-6 pb-8 bg-white pt-6">
          <Text className="text-xl font-semibold text-gray-900 mb-1">Why we ask for this</Text>
          <Text className="text-base text-gray-600 leading-6 mb-8">
            {InfoTexts[type as keyof typeof InfoTexts]}
          </Text>
          <View className="space-y-4 mb-8">
            <Pressable className="flex-row items-center py-3" onPress={handlePrivacyPress}>
              <Text className="text-[#004687] text-base font-medium">Privacy Policy</Text>
            </Pressable>
            <View className="h-px bg-gray-200" />
            <Pressable className="flex-row items-center py-3" onPress={handleTermsPress}>
              <Text className="text-[#004687] text-base font-medium">Terms & Conditions</Text>
            </Pressable>
          </View>
          <Pressable
            className="bg-primaryGreen py-4 px-6 rounded-xl"
            onPress={() => setIsVisible(false)}
          >
            <Text className="text-white font-semibold text-center text-base">Got it</Text>
          </Pressable>
        </View>
      </BottomSheet>

      <PrivacyModal isVisible={showPrivacyModal} onClose={() => setShowPrivacyModal(false)} />
      <TermsModal isVisible={showTermsModal} onClose={() => setShowTermsModal(false)} />
    </>
  );
};

export default InfoModal;
