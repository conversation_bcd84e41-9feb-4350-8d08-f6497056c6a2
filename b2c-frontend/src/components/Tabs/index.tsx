import React from 'react';
import { Pressable, ScrollView, Text, View } from 'react-native';
import { TabItemProps, TabsProps } from './types';

const TabItem: React.FC<TabItemProps> = ({ tab, isActive, onPress, styles, disabled = false }) => {
  return (
    <Pressable
      onPress={onPress}
      className={`${styles || ''} ${disabled ? 'opacity-50' : 'opacity-100'}`}
      disabled={disabled}
    >
      <Text
        className={`text-base font-medium text-center px-6 ${isActive ? 'text-green-700' : 'text-gray-500'}`}
      >
        {tab.label}
      </Text>
      <View className={`w-full h-0.5 ${isActive ? 'bg-green-800' : 'bg-gray-100'}`} />
    </Pressable>
  );
};

const Tabs: React.FC<TabsProps> = ({ tabs, activeTab, onTabChange, disabled = false }) => {
  const fewTabs = tabs.length <= 4;

  return (
    <View className="h-auto">
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        contentContainerStyle={fewTabs ? { flexGrow: 1 } : undefined}
      >
        <View className={fewTabs ? 'flex-row flex-1' : 'flex-row'}>
          {tabs.map((tab) => (
            <TabItem
              key={tab.id}
              tab={tab}
              isActive={activeTab === tab.id}
              onPress={() => onTabChange(tab.id)}
              styles={fewTabs ? 'flex-1' : ''}
              disabled={disabled}
            />
          ))}
        </View>
      </ScrollView>
    </View>
  );
};

export default Tabs;
