/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Video, getVideoMetaData } from 'react-native-compressor';
import { showToast } from '../toast';

const MAX_VIDEO_DURATION = 60;
const MAX_VIDEO_SIZE = 50 * 1024 * 1024;

export interface VideoInfo {
  uri: string;
  duration: number;
  size: number;
  width: number;
  height: number;
}

export const getVideoInfo = async (uri: string): Promise<VideoInfo> => {
  try {
    const info = await getVideoMetaData(uri);
    return {
      uri,
      duration: info.duration || 0,
      size: info.size || 0,
      width: info.width || 0,
      height: info.height || 0,
    };
  } catch (error) {
    throw new Error('Failed to get video information');
  }
};

export const validateVideoDuration = (duration: number): boolean => {
  return duration <= MAX_VIDEO_DURATION;
};

export const validateVideoSize = (size: number): boolean => {
  return size <= MAX_VIDEO_SIZE;
};

export const compressVideo = async (uri: string, targetSizeMB: number = 10): Promise<string> => {
  try {
    const targetSizeBytes = targetSizeMB * 1024 * 1024;

    const compressedUri = await Video.compress(
      uri,
      {
        compressionMethod: 'auto',
        maxSize: targetSizeBytes,
        minimumFileSizeForCompress: 2 * 1024 * 1024, // 2MB
      },
      (progress) => {
        // Progress callback - could be used for progress indicator
        console.log('Compression Progress: ', progress);
      },
    );

    return compressedUri;
  } catch (error) {
    console.error('Video compression failed:', error);
    throw new Error('Failed to compress video');
  }
};

export const processVideoForUpload = async (
  uri: string,
  filename?: string,
): Promise<{
  uri: string;
  type: string;
  filename: string;
  duration: number;
  isValid: boolean;
  errorMessage?: string;
}> => {
  try {
    // Get video information
    const videoInfo = await getVideoInfo(uri);

    // Validate duration
    if (!validateVideoDuration(videoInfo.duration)) {
      return {
        uri,
        type: 'video/mp4',
        filename: filename || 'video.mp4',
        duration: videoInfo.duration,
        isValid: false,
        errorMessage: `Video duration must be 1 minute or less. Current duration: ${Math.round(videoInfo.duration)}s`,
      };
    }

    // Validate size
    if (!validateVideoSize(videoInfo.size)) {
      return {
        uri,
        type: 'video/mp4',
        filename: filename || 'video.mp4',
        duration: videoInfo.duration,
        isValid: false,
        errorMessage: `Video size must be 50MB or less. Current size: ${Math.round(videoInfo.size / (1024 * 1024))}MB`,
      };
    }

    let processedUri = uri;

    // Compress if video is larger than 10MB
    if (videoInfo.size > 10 * 1024 * 1024) {
      showToast({
        type: 'info',
        message: 'Compressing Video',
        description: 'Please wait while we optimize your video...',
      });

      processedUri = await compressVideo(uri, 10);
    }

    return {
      uri: processedUri,
      type: 'video/mp4',
      filename: filename || 'video.mp4',
      duration: videoInfo.duration,
      isValid: true,
    };
  } catch (error) {
    console.error('Video processing failed:', error);
    return {
      uri,
      type: 'video/mp4',
      filename: filename || 'video.mp4',
      duration: 0,
      isValid: false,
      errorMessage: 'Failed to process video. Please try again.',
    };
  }
};
