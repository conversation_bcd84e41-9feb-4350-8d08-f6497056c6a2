import React from 'react';
import { ActivityIndicator, Platform, Pressable, StyleSheet, Text, View } from 'react-native';
import Config from 'react-native-config';
import Mapbox from '@rnmapbox/maps';
import { useSelector } from 'react-redux';
import AnonymousTimer from '@/src/components/AnonymousTimer';
import Modal from '@/src/components/Modal';
import { selectCurrentUser } from '@/src/redux/selectors/user';
import MaskActive from '@/src/assets/svgs/maskActive';
import MaskInactive from '@/src/assets/svgs/maskInactive';
import MapAvatar from '../MapAvatar';
import PeopleList from '../PeopleList';
import { useNearbyPeople } from './useHook';

const mapboxToken = Config.MAPBOX_ACCESS_TOKEN;
Mapbox.setAccessToken(mapboxToken);

const NearbyPeople = () => {
  const {
    userLocation,
    error,
    nearbyProfiles,
    loading,
    loadMoreProfiles,
    isAnonymous,
    toggleAnonymous,
    cooldownEnd,
    isModalVisible,
    toggleModal,
    isConfirming,
    isAnonymousLoaded,
  } = useNearbyPeople();
  const currentUserAvatar = useSelector(selectCurrentUser).avatar;

  const [loadedAvatarIds, setLoadedAvatarIds] = React.useState<Set<string>>(new Set());

  const handleLoadMore = () => {
    loadMoreProfiles().catch(console.error);
  };

  const handleAvatarLoaded = React.useCallback((profileId: string) => {
    setLoadedAvatarIds((prev) => {
      const newSet = new Set(prev);
      newSet.add(profileId);
      return newSet;
    });
  }, []);

  const peopleListHeight = Platform.OS === 'android' ? { height: 230 } : {};

  return (
    <View>
      <View className="flex-1">
        <View className="h-[500px] w-[100%] self-center mt-2 mb-4">
          {userLocation && isAnonymousLoaded ? (
            <Mapbox.MapView style={styles.map}>
              <Mapbox.PointAnnotation
                id="userLocation"
                coordinate={userLocation}
                anchor={{ x: 0.5, y: 0 }}
                key={`user-${loadedAvatarIds.has('currentUser') ? 'loaded' : 'loading'}-${isAnonymous}`}
              >
                <MapAvatar
                  uri={currentUserAvatar}
                  name="You"
                  profileId="currentUser"
                  onImageLoaded={handleAvatarLoaded}
                  isSelfUser={true}
                  isAnonymous={isAnonymous}
                />
              </Mapbox.PointAnnotation>
              {nearbyProfiles.map((profile) => (
                <Mapbox.PointAnnotation
                  key={`${profile.Profile.id}-${loadedAvatarIds.has(profile.Profile.id) ? 'loaded' : 'loading'}`}
                  id={profile.Profile.id}
                  coordinate={[parseFloat(profile.longitude), parseFloat(profile.latitude)]}
                  anchor={{ x: 0.5, y: 0 }}
                >
                  <MapAvatar
                    uri={profile.Profile.avatar}
                    name={profile.Profile.name}
                    profileId={profile.Profile.id}
                    onImageLoaded={handleAvatarLoaded}
                    isAnonymous={isAnonymous}
                  />
                </Mapbox.PointAnnotation>
              ))}
              <Mapbox.Camera
                centerCoordinate={userLocation}
                zoomLevel={5}
                animationMode={'flyTo'}
              />
            </Mapbox.MapView>
          ) : (
            <View className="flex-1 justify-center items-center p-4">
              {error ? (
                <Text className="text-center text-gray-600 text-lg">
                  Oh no! <Text className="font-bold">Location access is required</Text> to see
                  nearby people. Please enable location services for this app.
                </Text>
              ) : (
                <>
                  <ActivityIndicator size="large" color="#6B7280" className="mb-4" />
                  <Text className="text-center text-gray-600 text-lg">
                    Searching for your location... <Text className="font-bold">Hang tight! ✨</Text>
                  </Text>
                </>
              )}
            </View>
          )}
        </View>
      </View>
      {userLocation && !error && (
        <View style={[styles.peopleListContainer, peopleListHeight]}>
          <PeopleList
            data={nearbyProfiles}
            onLoadMore={handleLoadMore}
            loading={loading}
            isAnonymous={isAnonymous}
          />
        </View>
      )}
      <Pressable style={styles.anonymous} onPress={isAnonymous ? toggleAnonymous : toggleModal}>
        {isAnonymous ? <MaskActive width={5} height={5} /> : <MaskInactive width={5} height={5} />}
      </Pressable>
      <View style={styles.anonymousTimer}>
        <AnonymousTimer cooldownEnd={cooldownEnd} />
      </View>
      <Modal
        isVisible={isModalVisible}
        title="Be Anonymous"
        onCancel={toggleModal}
        onConfirm={toggleAnonymous}
        confirmText="Confirm"
        description="You cannot change back the status for 1 hour"
        confirmButtonMode="danger"
        isConfirming={isConfirming}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  map: {
    flex: 1,
    borderRadius: 0,
  },
  peopleListContainer: {
    position: 'absolute',
    top: 460,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderRadius: 0,
    paddingVertical: 12,
  },
  anonymous: {
    position: 'absolute',
    right: 20,
    top: 20,
    zIndex: 1000,
  },
  anonymousTimer: {
    position: 'absolute',
    right: 20,
    top: 75,
    zIndex: 1000,
  },
});

export default NearbyPeople;
