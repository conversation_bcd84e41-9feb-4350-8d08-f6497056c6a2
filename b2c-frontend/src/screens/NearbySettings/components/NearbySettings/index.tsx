import { useState } from 'react';
import { ActivityIndicator, Pressable, ScrollView, Text, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import { Controller } from 'react-hook-form';
import BackButton from '@/src/components/BackButton';
import TextInput from '@/src/components/TextInput';
import { NearbyStackParamListI } from '@/src/navigation/types';
import AddItem from '@/src/assets/svgs/AddItem';
import TrashBin from '@/src/assets/svgs/TrashBin';
import InformationText from '../InformationText';
import type { EditNearbySettingsPropsI, MapboxSuggestion } from './types';
import { useNearbySettings } from './useHook';

const EditNearbySettings = ({ onBack }: EditNearbySettingsPropsI) => {
  const navigation = useNavigation<StackNavigationProp<NearbyStackParamListI>>();
  const {
    methods,
    handleAddOtherLocation,
    isOtherLocationAdded,
    handleDeleteOtherLocation,
    loading,
    onSubmit,
    isSelfLocationValid,
    isOtherLocationValid,
    selfLocation,
    otherLocation,
  } = useNearbySettings();

  const { control, handleSubmit } = methods;

  const [enabled, setEnabled] = useState(false);

  const handleSelfLocationPress = () => {
    navigation.navigate('Location', { type: 'self' });
    setEnabled(true);
  };

  const handleOtherLocationPress = () => {
    navigation.navigate('Location', { type: 'other' });
    setEnabled(true);
  };

  return (
    <ScrollView className="flex-1 bg-white" showsVerticalScrollIndicator={false}>
      <View className="flex-row items-center justify-between py-4">
        <BackButton onBack={onBack} label="Nearby Settings" />
        <Pressable onPress={handleSubmit(onSubmit)} disabled={!enabled} className="pr-2">
          <Text className={`text-lg font-medium ${enabled ? 'text-[#448600]' : 'text-gray-400'}`}>
            {loading ? 'Applying...' : 'Apply'}
          </Text>
        </Pressable>
      </View>
      <View className="gap-4 px-4">
        <InformationText />

        <Pressable onPress={handleSelfLocationPress} className="mt-3">
          <Text className="text-sm font-medium text-black mb-2 leading-4">Event Location</Text>
          <View className="border border-[#D4D4D4] w-full px-4 py-5 rounded-xl bg-white flex items-center flex-row">
            <Text
              className={`
                ${isSelfLocationValid ? 'text-black' : 'text-gray-400'}
                text-base leading-5
                `}
            >
              {isSelfLocationValid ? selfLocation.name : 'Enter Location'}
            </Text>
          </View>
        </Pressable>

        <Pressable onPress={handleAddOtherLocation}>
          <AddItem color={isOtherLocationAdded ? 'gray' : '#448600'} />
        </Pressable>

        {(isOtherLocationAdded || isOtherLocationValid) && (
          <View>
            <Pressable onPress={handleOtherLocationPress} className="mt-3">
              <Text className="text-sm font-medium text-black mb-2 leading-4">Other Location</Text>
              <View className="border border-[#D4D4D4] w-full px-4 py-5 rounded-xl bg-white flex items-center flex-row">
                <Text
                  className={`
                ${isOtherLocationValid ? 'text-black' : 'text-gray-400'}
                text-base leading-5
                `}
                >
                  {isOtherLocationValid ? otherLocation.name : 'Enter Location'}
                </Text>
              </View>
            </Pressable>
            <View className="flex-row justify-between">
              <View />
              <Pressable
                className="m-3"
                onPress={() => {
                  handleDeleteOtherLocation();
                  setEnabled(true);
                }}
              >
                <TrashBin width={2.5} height={2.5} color="red" />
              </Pressable>
            </View>
          </View>
        )}
        <Controller
          control={control}
          name="radius"
          rules={{
            required: 'Radius is required',
            validate: (value) => (value > 0 ? true : 'Radius must be greater than 0'),
          }}
          render={({ field: { onChange, value }, fieldState: { error } }) => (
            <TextInput
              label="Radius(km)"
              value={value?.toString()}
              onChangeText={(text) => {
                const numValue = text ? Number.parseFloat(text) : 0;
                onChange(numValue);
                setEnabled(true);
              }}
              keyboardType="numeric"
              error={error?.message}
            />
          )}
        />
      </View>
    </ScrollView>
  );
};

export default EditNearbySettings;
