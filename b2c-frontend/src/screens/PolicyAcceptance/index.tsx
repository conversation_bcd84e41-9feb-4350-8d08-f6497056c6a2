import { View, Text, ScrollView, ActivityIndicator, StyleSheet } from 'react-native';
import Markdown from 'react-native-markdown-display';
import { Controller } from 'react-hook-form';
import Button from '@/src/components/Button';
import Checkbox from '@/src/components/Checkbox';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import usePolicyAcceptance from './useHook';

const PolicyAcceptance = () => {
  const {
    control,
    isSubmitting,
    onSubmit,
    handleSubmit,
    isValid,
    activeTab,
    setActiveTab,
    privacyContent,
    termsContent,
    loading,
  } = usePolicyAcceptance();

  const tabs = [
    { id: 'privacy', label: 'Privacy Policy' },
    { id: 'terms', label: 'Terms & Conditions' },
  ];

  const markdownStyles = StyleSheet.create({
    body: {
      fontSize: 16,
      lineHeight: 24,
      color: '#374151',
    },
    heading1: {
      fontSize: 24,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 16,
      marginTop: 24,
    },
    heading2: {
      fontSize: 20,
      fontWeight: 'bold',
      color: '#111827',
      marginBottom: 12,
      marginTop: 20,
    },
    heading3: {
      fontSize: 18,
      fontWeight: '600',
      color: '#111827',
      marginBottom: 8,
      marginTop: 16,
    },
    paragraph: {
      marginBottom: 12,
      lineHeight: 22,
    },
    list_item: {
      marginBottom: 8,
    },
    bullet_list: {
      marginBottom: 16,
    },
    ordered_list: {
      marginBottom: 16,
    },
    strong: {
      fontWeight: 'bold',
    },
    em: {
      fontStyle: 'italic',
    },
    link: {
      color: '#004687',
      textDecorationLine: 'underline',
    },
  });

  const renderContent = () => {
    if (loading) {
      return (
        <View className="flex-1 justify-center items-center py-20">
          <ActivityIndicator size="small" color="#448600" />
        </View>
      );
    }

    const content = activeTab === 'privacy' ? privacyContent : termsContent;
    const cleanContent = content?.replace(/\\\n/g, '\n').replace(/\\"/g, '"').replace(/\\'/g, "'");

    return (
      <ScrollView className="flex-1 px-4" showsVerticalScrollIndicator={false}>
        {cleanContent && <Markdown style={markdownStyles}>{cleanContent}</Markdown>}
      </ScrollView>
    );
  };

  return (
    <SafeArea>
      <View className="flex-1 px-4 py-6">
        <View className="mb-6">
          <Text className="text-2xl font-bold text-gray-900 text-center mb-2">Privacy & Terms</Text>
        </View>

        <View className="flex-1 bg-white rounded-lg border border-gray-200">
          <View className="py-4">
            <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
          </View>

          {renderContent()}
        </View>
        <View className="my-6">
          <Controller
            control={control}
            name="ppAndTNC"
            rules={{
              required: 'You must accept our policies before proceeding',
            }}
            render={({ field: { onChange, value } }) => (
              <View>
                <Checkbox
                  label=""
                  onValueChange={onChange}
                  checked={value}
                  className="z-10 mt-1"
                  size={20}
                />
                <View className="flex-1 ml-10">
                  <Text className="text-base text-gray-70 -mt-6">I agree to above policies</Text>
                </View>
              </View>
            )}
          />
        </View>
        <Button
          onPress={handleSubmit(onSubmit)}
          disabled={!isValid || isSubmitting}
          label="Continue"
          variant={isValid ? 'primary' : 'tertiary'}
          loading={isSubmitting}
          labelClassName="text-base font-medium"
        />
      </View>
    </SafeArea>
  );
};

export default PolicyAcceptance;
