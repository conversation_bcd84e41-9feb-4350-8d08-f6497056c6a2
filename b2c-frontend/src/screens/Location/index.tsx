import { type ReactNode, useState } from 'react';
import { Pressable, Text, View } from 'react-native';
import { RouteProp, useNavigation, useRoute } from '@react-navigation/native';
import { useDispatch } from 'react-redux';
import BackButton from '@/src/components/BackButton';
import SafeArea from '@/src/components/SafeArea';
import Tabs from '@/src/components/Tabs';
import {
  setLocationData,
  setOtherLocation,
  setSelfLocation,
} from '@/src/redux/slices/announcement/announcementSlice';
import { MapboxContext } from '@/src/redux/slices/announcement/types';
import { clearSelection } from '@/src/redux/slices/entitysearch/searchSlice';
import type { AppDispatch } from '@/src/redux/store';
import { NearbyStackParamListI } from '@/src/navigation/types';
import Map from './components/map';
import Port from './components/port';

type SelectedLocation = {
  center: number[];
  context: MapboxContext[];
  id: string;
  geometry: {
    type: string;
    coordinates: number[];
  };
  place_name: string;
  place_type: string[];
  properties: {
    accuracy: string;
    mapbox_id: string;
  };
  relevance: number;
  text: string;
  type: string;
};

const LocationScreen = () => {
  const navigation = useNavigation();
  type RouteProps = RouteProp<NearbyStackParamListI, 'Location'>;
  const route = useRoute<RouteProps>();
  const type = route.params.type;
  const [selectedLocation, setSelectedLocation] = useState<SelectedLocation | null>(null);
  const dispatch = useDispatch<AppDispatch>();

  const handleDoneClick = () => {
    if (selectedLocation) {
      if (type === 'create') {
        dispatch(setLocationData(selectedLocation));
      } else if (type === 'self') {
        dispatch(
          setSelfLocation({
            coords: {
              longitude: selectedLocation.center[0],
              latitude: selectedLocation.center[1],
            },
            name: selectedLocation.place_name,
          }),
        );
      } else if (type === 'other') {
        dispatch(
          setOtherLocation({
            coords: {
              longitude: selectedLocation.center[0],
              latitude: selectedLocation.center[1],
            },
            name: selectedLocation.place_name,
          }),
        );
      }

      dispatch(clearSelection('city'));
    }
  };

  const tabs = [
    { id: 'map', label: 'Map' },
    { id: 'port', label: 'Port' },
  ];
  const [activeTab, setActiveTab] = useState('map');

  const tabComponents: { [key: string]: ReactNode } = {
    port: <Port />,
    map: <Map onLocationSelect={setSelectedLocation} />,
  };

  return (
    <SafeArea>
      <View className="flex flex-row justify-between items-center">
        <BackButton onBack={() => navigation.goBack()} />
        <Pressable
          className="pr-3"
          onPress={() => {
            handleDoneClick();
            navigation.goBack();
          }}
        >
          <Text className={`text-lg font-medium text-[#448600]`}>Done</Text>
        </Pressable>
      </View>
      {type === 'create' ? (
        <>
          <Tabs tabs={tabs} activeTab={activeTab} onTabChange={setActiveTab} />
          <View className="flex-1">{tabComponents[activeTab]}</View>
        </>
      ) : (
        <Map onLocationSelect={setSelectedLocation} />
      )}
    </SafeArea>
  );
};

export default LocationScreen;
