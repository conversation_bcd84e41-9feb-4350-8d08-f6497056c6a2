import type React from 'react';
import { View, Text } from 'react-native';
import type { PostExternalClientI } from '@/src/networks/content/types';
import SearchItem from '../SearchItem';
import type { SearchResultItemI } from '../SearchResults/types';
import type { PopularResultsProps } from './types';

const PopularResults: React.FC<PopularResultsProps> = ({ suggestions, activeTab }) => {
  const getIcon = () => {
    switch (activeTab) {
      case 'people':
        return '👥';
      case 'post':
        return '📝';
      case 'ship':
        return '🚢';
      case 'port':
        return '⚓';
      default:
        return '⭐';
    }
  };

  return (
    <View className="flex-1">
      <View className="px-4 pt-4 pb-3">
        <View className="flex-row items-center">
          <Text className="text-xl mr-2">{getIcon()}</Text>
          <Text className="text-gray-800 text-base font-semibold">Suggestions</Text>
        </View>
      </View>
      <View className="bg-white pt-2 pb-8">
        {suggestions.map((item: SearchResultItemI | PostExternalClientI) => {
          return (
            <SearchItem
              key={`${(item as SearchResultItemI).profileId || (item as SearchResultItemI).imo || (item as SearchResultItemI).unLocode || (item as PostExternalClientI).id}-${activeTab}`}
              item={item}
              activeTab={activeTab}
              isPopularMode={true}
            />
          );
        })}
      </View>
    </View>
  );
};

export default PopularResults;
