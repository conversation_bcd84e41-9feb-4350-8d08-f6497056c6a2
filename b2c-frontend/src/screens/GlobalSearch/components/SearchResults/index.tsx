import type React from 'react';
import { useEffect, useRef } from 'react';
import { ActivityIndicator, View, RefreshControl, Text } from 'react-native';
import { FlatList } from 'react-native-gesture-handler';
import { useSelector, useDispatch } from 'react-redux';
import { selectSearchPostsByTerm } from '@/src/redux/selectors/content';
import { setSearchPosts } from '@/src/redux/slices/content/contentSlice';
import type { PostExternalClientI } from '@/src/networks/content/types';
import SearchItem from '../SearchItem';
import type { SearchResultsProps, SearchResultItemI } from './types';

const SearchResults: React.FC<SearchResultsProps> = ({
  activeTab,
  loading,
  searchResults,
  onLoadMore,
  onRefresh,
  refreshing,
  searchText,
  onError,
}) => {
  const searchPosts = useSelector((state) => selectSearchPostsByTerm(state, searchText || ''));
  const dispatch = useDispatch();
  const scrollRef = useRef(null);

  const renderItem = ({ item, index }: { item: unknown; index: number }) => {
    return (
      <SearchItem
        item={item as SearchResultItemI | PostExternalClientI}
        activeTab={activeTab}
        onError={onError}
        postIndex={index}
        parentScrollRef={scrollRef}
      />
    );
  };

  const renderFooter = () => {
    if (!loading) return null;
    return (
      <View className="py-4 flex items-center justify-center">
        <ActivityIndicator size="small" />
      </View>
    );
  };

  const getCurrentData = () => {
    if (activeTab === 'post') {
      return searchPosts.length > 0 ? searchPosts : searchResults?.data || [];
    }
    return searchResults?.data || [];
  };

  const currentData = getCurrentData();

  useEffect(() => {
    if (activeTab === 'post' && searchResults?.data && searchText) {
      const postsData = searchResults.data as PostExternalClientI[];
      if (postsData.length > 0) {
        dispatch(
          setSearchPosts({
            searchTerm: searchText,
            posts: postsData.map((post) => ({
              ...post,
              reactionsCount: post.reactionsCount || 0,
              totalCommentsCount: post.totalCommentsCount || 0,
              isLiked: post.isLiked || false,
            })),
          }),
        );
      }
    }
  }, [searchResults?.data, activeTab, searchText, dispatch]);

  return (
    <View className="flex-1">
      <FlatList
        ref={scrollRef}
        data={currentData}
        extraData={searchPosts}
        keyExtractor={(item, index) =>
          `${activeTab}-${(item as SearchResultItemI).profileId || (item as SearchResultItemI).id || index}`
        }
        renderItem={renderItem}
        onEndReached={onLoadMore}
        onEndReachedThreshold={0.8}
        refreshControl={
          <RefreshControl enabled={true} refreshing={refreshing} onRefresh={onRefresh} />
        }
        ListFooterComponent={renderFooter}
        ListEmptyComponent={
          !loading && currentData.length === 0 ? (
            <View className="flex-1 justify-center items-center px-8 py-10">
              <Text className="text-xl font-semibold text-gray-800 text-center mb-2">
                No results found
              </Text>
              <Text className="text-gray-500 text-center leading-relaxed">
                Try a different search query or check the spelling.
              </Text>
            </View>
          ) : null
        }
        contentContainerStyle={{
          flexGrow: 1,
          backgroundColor: 'white',
        }}
        removeClippedSubviews={false}
        maxToRenderPerBatch={10}
        windowSize={10}
      />
    </View>
  );
};

export default SearchResults;
