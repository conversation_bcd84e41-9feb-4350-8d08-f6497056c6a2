import type { GlobalSearchCategory, GlobalSearchResponse } from '@/src/utilities/search/types';

export interface SearchResultsProps {
  activeTab: GlobalSearchCategory;
  loading: boolean;
  searchResults: GlobalSearchResponse;
  onTabChange?: (tab: GlobalSearchCategory) => void;
  onLoadMore?: () => void;
  onRefresh: () => void;
  refreshing: boolean;
  searchText?: string;
  onError?: (error: Error) => void;
}

export interface SearchCategoryTabs {
  id: GlobalSearchCategory;
  label: string;
}

export interface SearchResultItemI {
  id: string;
  name: string;
  profileId?: string;
  avatar?: string;
  designation?: { name: string };
  entity?: { name: string };
  priority?: number;
  dataType: 'raw' | 'master';
  imo?: string;
  matchedName?: string;
  imageUrl?: string;
  unLocode?: string;
  city?: { name: string };
  country?: { name: string };
}
