/*
Copyright (c) 2025-present Navicater Solutions

This source code is licensed under the license found in the
LICENSE file in the root directory of this source tree.
*/
import { Text, View } from 'react-native';
import type { ScoreboardItemProps } from '../../types';

const ScoreboardItem = ({ category, score, index }: ScoreboardItemProps) => {
  const ratio = (index / 100).toFixed(2);

  return (
    <View className="bg-[#F6F4F4] rounded-lg p-4 mb-3">
      <View className="flex-row items-center">
        <View className="flex-1">
          <Text className="text-base font-medium text-[#4B4B4B]">{category}</Text>
        </View>
        <View className="w-32 items-center">
          <Text className="text-base font-semibold text-[#7E7D7D]">{score}</Text>
        </View>
        <View className="w-32 items-end">
          <Text className="text-base font-semibold text-[#7E7D7D]">{ratio}</Text>
        </View>
      </View>
    </View>
  );
};

export default ScoreboardItem;
