import { ActivityIndicator, Pressable, View } from 'react-native';
import { useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import BackButton from '@/src/components/BackButton';
import UsersList from '@/src/components/UsersList';
import { ListItem } from '@/src/components/UsersList/types';
import { NearbyStackParamListI } from '@/src/navigation/types';
import AddConnection from '@/src/assets/svgs/AddConnection';
import { PeopleAttendingPropsI } from './types';
import { usePeopleAttending } from './useHook';

const PeopleAttending = ({ onBack, announcementId }: PeopleAttendingPropsI) => {
  const { people, addConnection, loadMorePeople, loading, loadingMore, hasMore, refreshPeople } =
    usePeopleAttending(announcementId);
  const navigation = useNavigation<StackNavigationProp<NearbyStackParamListI>>();
  const onUserPress = (item: ListItem) => {
    navigation.navigate('OtherUserProfile', {
      profileId: item.Profile.id,
      fromTabPress: false,
    });
  };

  const renderActions = (item: ListItem) => {
    if (item.status === 'NOT_CONNECTED') {
      return (
        <Pressable
          className="border border-[#448600] p-1 rounded-lg"
          onPress={() => addConnection(item.Profile.id)}
        >
          <AddConnection />
        </Pressable>
      );
    }
  };

  return (
    <View>
      <BackButton onBack={onBack} label="Attendees" />
      <UsersList
        data={people as unknown as ListItem[]}
        renderActions={renderActions}
        onLoadMore={loadMorePeople}
        loading={loading}
        onRefresh={refreshPeople}
        onPress={onUserPress}
      />
    </View>
  );
};

export default PeopleAttending;
