import AppError from '@classes/AppError';
import { CountryIso2Schema, CursorPaginationSchema, IdTypeSchema, UUIDSchema } from '@schemas/common/common';
import { DateSchema, Time24Schema } from '@schemas/common/date';
import { CityMapboxSchema } from '@schemas/master/city';
import { LatitudeSchema, LongitudeSchema, UnLocodeSchema } from '@schemas/port/common';
import { AddressRawDataMapboxSchema, postCodeMapBoxSchema } from '@schemas/rawData/addressRawData';
import { getCurrentDate, isLessThanEqualDateRange } from '@utils/data/date';
import { isEmpty, isFilled } from '@utils/data/object';
import z from 'zod';

export const PortPortSearchClientWithCoordinatesSchema = z.object({
  name: z.string(),
  unLocode: UnLocodeSchema,
  latitude: LatitudeSchema,
  longitude: LongitudeSchema,
  dataType: z.string(),
});

export const AnnouncementCreateOneSchema = z
  .object({
    title: z.string().min(3).max(100),
    description: z.string().min(3).max(1000),
    latitude: LatitudeSchema.optional(),
    longitude: LongitudeSchema.optional(),
    addressMapBox: AddressRawDataMapboxSchema?.optional(),
    city: IdTypeSchema.optional(),
    port: PortPortSearchClientWithCoordinatesSchema.optional(),
    cityMapBox: CityMapboxSchema.optional(),
    countryIso2: CountryIso2Schema.optional(),
    startDate: DateSchema,
    endDate: DateSchema,
    startTime: Time24Schema,
    endTime: Time24Schema,
    postCodeMapBox: postCodeMapBoxSchema.optional(),
  })
  .superRefine((data, _ctx) => {
    if (isEmpty(data?.city) && isEmpty(data?.cityMapBox)) {
      throw new AppError('ANC001');
    }
    if (isEmpty(data?.addressMapBox)) {
      if (isFilled(data?.cityMapBox)) {
        throw new AppError('ANC002');
      }
    }
    if (!isLessThanEqualDateRange(data.startDate, data.endDate)) {
      throw new AppError('ANC003');
    }
  });
export type AnnouncementCreateOneI = z.infer<typeof AnnouncementCreateOneSchema>;

export const CoreAnnouncementFetchParamsSchema = CursorPaginationSchema.merge(
  z.object({
    coordinates: z
      .array(
        z.object({
          latitude: LatitudeSchema,
          longitude: LongitudeSchema,
        }),
      )
      .min(1)
      .max(2),
    radius: z.number().min(1).default(40),
  }),
);
export type CoreAnnouncementFetchParamsI = z.infer<typeof CoreAnnouncementFetchParamsSchema>;

export const AnnouncementDeleteSchema = z.object({
  id: UUIDSchema,
});
export type AnnouncementDeleteOneI = z.infer<typeof AnnouncementDeleteSchema>;
