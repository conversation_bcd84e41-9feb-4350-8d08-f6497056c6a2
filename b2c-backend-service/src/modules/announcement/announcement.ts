import AppError from '@classes/AppError';
import { prismaPG } from '@config/db';
import { CoreAnnouncementFetchItemI, CoreAnnouncementFetchItemSQLI } from '@interfaces/announcement/announcement';
import { CursorDataI, NumberNullI } from '@interfaces/common/data';
import type { FastifyStateI } from '@interfaces/common/declaration';
import { CityClientI } from '@interfaces/master/city';
import Master from '@modules/master';
import { RawDataModule } from '@modules/rawData';
import User from '@modules/user';
import { Prisma } from '@prisma/postgres';
import type { Announcement } from '@prisma/postgres';
import type {
  AnnouncementCreateOneI,
  AnnouncementDeleteOneI,
  CoreAnnouncementFetchParamsI,
} from '@schemas/announcement/announcement';
import { isFilled } from '@utils/data/object';

export const CoreAnnouncementModule = {
  createOne: async (
    state: FastifyStateI,
    {
      title,
      description,
      latitude,
      longitude,
      addressMapBox,
      city,
      port,
      cityMapBox,
      countryIso2,
      startDate,
      endDate,
      startTime,
      endTime,
      postCodeMapBox,
    }: AnnouncementCreateOneI,
  ): Promise<Partial<Omit<Announcement, 'cursorId'> & { cursorId: string }>> => {
    let cityResult: CityClientI = null;
    const cityFilter: Prisma.CityRawDataWhereInput = {};
    let cityInput: Prisma.CityRawDataUncheckedCreateInput;
    if (isFilled(city)) {
      cityFilter.id = city.id;
    } else if (isFilled(cityMapBox)) {
      cityFilter.countryIso2 = countryIso2;
      cityFilter.OR = [
        {
          mapboxId: {
            equals: cityMapBox.id,
            mode: 'insensitive',
          },
        },
        {
          name: {
            contains: cityMapBox.name,
            mode: 'insensitive',
          },
        },
      ];
      cityInput = {
        mapboxId: cityMapBox.id,
        name: cityMapBox.name,
        countryIso2,
        latitude,
        longitude,
      };
    }
    if (isFilled(city)) {
      cityResult = await Master.CityModule.fetchsert(cityInput, cityFilter, city.dataType);
    }
    const addressRawData = await (isFilled(addressMapBox)
      ? RawDataModule.AddressRawDataModule.fetchsert(
          {
            mapboxId: addressMapBox.id,
            text: addressMapBox.text,
            latitude,
            longitude,
            countryIso2,
            city,
            cityMapBox,
            postCodeMapBox,
          },
          {
            mapboxId: addressMapBox.id,
          },
        )
      : null);

    const input: Prisma.AnnouncementUncheckedCreateInput = {
      profileId: state.profileId,
      countryIso2,
      title,
      description,
      startDate,
      endDate,
      startTime,
      endTime,
      totalAttendees: 1,
    };
    if (countryIso2) {
      input.countryIso2 = countryIso2;
    }
    if (isFilled(city)) {
      if (cityResult.dataType === 'master') {
        input.cityId = cityResult.id;
      } else {
        input.cityRawDataId = cityResult.id;
      }
      if (port.dataType === 'master') {
        input.portUnLocode = port.unLocode;
      } else {
        input.portRawDataUnLocode = port.unLocode;
      }
      input.latitude = port.latitude;
      input.longitude = port.longitude;
    }
    if (addressRawData) {
      input.addressRawDataId = addressRawData.id;
      input.latitude = latitude;
      input.longitude = longitude;
    }

    input.RSVP = {
      create: {
        profileId: state.profileId,
      },
    };

    const announcement = await prismaPG.announcement.create({ data: input, select: { id: true, cursorId: true } });

    if (!announcement) {
      throw new AppError('ANC005');
    }
    return {
      ...announcement,
      cursorId: announcement.cursorId.toString(),
    };
  },

  fetchAnnouncements: async (
    state: FastifyStateI,
    { cursorId, coordinates, radius = 40, pageSize }: CoreAnnouncementFetchParamsI,
  ): Promise<CursorDataI<CoreAnnouncementFetchItemI>> => {
    const selfProfileId = state.profileId;
    const radiusInMeters = radius * 1000;
    let locationFilter = Prisma.empty;
    if (coordinates && coordinates.length > 0) {
      locationFilter = Prisma.sql`AND (
      ${Prisma.join(
        coordinates.map(
          (coord) =>
            Prisma.sql`ST_DWithin(
            ST_SetSRID(ST_MakePoint(${coord.longitude}, ${coord.latitude}), 4326)::geography,
            ST_SetSRID(ST_MakePoint(a."longitude", a."latitude"), 4326)::geography,
            ${radiusInMeters}
          )`,
        ),
        ' OR ',
      )}
    )`;
    }

    const announcementsTemp: CoreAnnouncementFetchItemSQLI[] = await prismaPG.$queryRaw<
      CoreAnnouncementFetchItemSQLI[]
    >(
      Prisma.sql`
    SELECT
    a."id" as "announcementId",
    a."cursorId",
    a."title",
    a."description",
    a."latitude",
    a."longitude",
    a."cityId",
    a."cityRawDataId",
    c."id" as "cityId",
    c."name" as "cityName",
    crd."id" as "cityRawDataId",
    crd."name" as "cityRawDataName",
    a."countryIso2",
    a."startDate",
    a."endDate",
    a."startTime",
    a."endTime",
    a."totalAttendees",
    a."createdAt",
    a."updatedAt",
json_build_object(
  'id', p."id",
  'name', p."name",
  'avatar', p."avatar",
  'designationText', p."designationText",
  'designationAlternativeId', p."designationAlternativeId",
  'designationRawDataId', p."designationRawDataId",
  'entityText', p."entityText",
  'entityId', p."entityId",
  'entityRawDataId', p."entityRawDataId"
) AS "profile",
json_build_object(
  'id', ard."id",
  'text', ard."text"
) AS "addressRawData",
   CASE
  WHEN rsvp."profileId" IS NOT NULL OR a."profileId" = ${selfProfileId}::uuid
  THEN TRUE
  ELSE FALSE
END AS "isRSVPed",
   (
          SELECT json_agg(att)
          FROM (
            SELECT json_build_object('id', p2."id", 'name', p2."name",'avatar', p2."avatar") as att
            FROM "announcement"."RSVP" r2
            JOIN "user"."Profile" p2 ON p2."id" = r2."profileId"
            WHERE r2."announcementId" = a."id"
              AND p2."status" = 'ACTIVE'
              AND p2."id" != a."profileId"
            ORDER BY r2."createdAt" ASC
            LIMIT 2
          ) AS topAttendees
        )AS "topAttendeesRaw",
     ${
       coordinates && coordinates.length > 0
         ? Prisma.sql`(
              SELECT MIN(ST_Distance(
                ST_SetSRID(ST_MakePoint(c.longitude, c.latitude), 4326)::geography,
                ST_SetSRID(ST_MakePoint(a."longitude", a."latitude"), 4326)::geography
              ))
              FROM (VALUES ${Prisma.join(
                coordinates.map((c) => Prisma.sql`(${c.longitude}, ${c.latitude})`),
                ', ',
              )}) AS c(longitude, latitude)
            ) AS "distanceInMeters"`
         : Prisma.sql`NULL AS "distanceInMeters"`
     }

    FROM "announcement"."Announcement" a
      LEFT JOIN "announcement"."RSVP" rsvp
        ON rsvp."announcementId" = a."id"
        AND rsvp."profileId" = ${selfProfileId}::uuid
      LEFT JOIN "user"."Profile" p
        ON p."id" = a."profileId"
        AND p."status" = 'ACTIVE'
      LEFT JOIN "network"."BlockedProfile" b1
        ON b1."blockerId" = ${selfProfileId}::uuid
        AND b1."blockedId" = p."id"
      LEFT JOIN "network"."BlockedProfile" b2
        ON b2."blockerId" = p."id"
        AND b2."blockedId" = ${selfProfileId}::uuid
      LEFT JOIN "rawData"."AddressRawData" ard
       ON ard."id" = a."addressRawDataId"
      LEFT JOIN "master"."City" c
       ON c."id" = a."cityId"
      LEFT JOIN "rawData"."CityRawData" crd
       ON crd."id" = a."cityRawDataId"
      WHERE
        (
          a."endDate" > CURRENT_DATE
          OR
          (
            a."endDate" = CURRENT_DATE AND a."endTime" > CURRENT_TIME
          )
        )
  AND a."latitude" IS NOT NULL
  AND a."longitude" IS NOT NULL
  AND b1."blockedId" IS NULL
  AND b2."blockedId" IS NULL
  ${locationFilter}
    ${cursorId ? Prisma.sql`AND a."cursorId" < ${cursorId}` : Prisma.empty}
    ORDER BY
        ${coordinates && coordinates.length > 0 ? Prisma.sql`"distanceInMeters" ASC,` : Prisma.empty}
      a."cursorId" DESC
    LIMIT ${pageSize + 1}
  `,
    );

    const hasNextPage = announcementsTemp.length > pageSize;
    const results = hasNextPage ? announcementsTemp.slice(0, pageSize) : announcementsTemp;
    const nextCursorId: NumberNullI = hasNextPage ? Number(results[results.length - 1].cursorId) : null;

    const announcements: CoreAnnouncementFetchItemI[] = results.map((item) => ({
      ...item,
      cursorId: Number(item.cursorId),
      latitude: item.latitude ? Number(item.latitude) : undefined,
      longitude: item.longitude ? Number(item.longitude) : undefined,
      isRSVPed: item.isRSVPed,
      profile: User.ProfileModule.transformProfile(item.profile),
      totalAttendees: Number(item.totalAttendees) || 0,
      addressRawData: item.addressRawData,
      city: item.cityId
        ? {
            id: item.cityId,
            name: item.cityName,
            dataType: 'master' as const,
          }
        : item.cityRawDataId
          ? {
              id: item.cityRawDataId,
              name: item.cityRawDataName,
              dataType: 'raw' as const,
            }
          : null,
      attendeesSummary: (() => {
        const preview = item.topAttendeesRaw ?? [];
        const topNames = preview.map((a) => a.name);
        const othersCount = (Number(item.totalAttendees) || 0) - topNames.length;
        return {
          topAttendees: topNames,
          othersCount: Math.max(othersCount, 0),
        };
      })(),
    }));

    return {
      data: announcements,
      nextCursorId,
    };
  },

  deleteOne: async (state: FastifyStateI, { id }: AnnouncementDeleteOneI): Promise<void> => {
    const selfProfileId = state.profileId;
    const creatorId = await prismaPG.announcement.findUnique({
      where: {
        id: id,
      },
      select: {
        profileId: true,
      },
    });
    if (creatorId.profileId !== selfProfileId) {
      throw new AppError('ANC008');
    }

    await prismaPG.announcement.delete({
      where: {
        id: id,
      },
    });
  },
};
